# shell-toolkit

一個存放常用 shell 腳本的工具集，包含適用於 AIX 系統的 KSH（KornShell）腳本以及一般 Linux/macOS 環境的 Bash 腳本，涵蓋檔案處理、系統管理與自動化任務。

## 專案目標
- 提供實用的 shell 腳本，簡化日常工作流程。
- 分類支援 AIX 環境（KSH）和通用環境（Bash）。
- 易於維護和擴展，適合開發者、系統管理員和 DevOps 人員。

## 目錄結構
```
shell-toolkit/
├── scripts/
│   ├── ksh/              # AIX 系統適用的 KSH 腳本
│   │   ├── backup.ksh    # 檔案備份腳本
│   │   ├── monitor.ksh   # 系統監控腳本
│   ├── bash/             # 通用 Bash 腳本（Linux/macOS）
│   │   ├── rename.sh     # 批次檔案重命名
│   │   ├── log_clean.sh  # 日誌清理腳本
├── README.md             # 專案說明文件
├── LICENSE               # 授權文件（可選）
└── .gitignore            # Git 忽略文件
```

## 環境需求
- **KSH 腳本**：
  - AIX 系統（或支援 KSH 的環境）
  - KornShell (ksh) 環境
  - 常用工具：`tar`, `grep`, `awk`（視腳本需求而定）
- **Bash 腳本**：
  - Linux 或 macOS 系統
  - Bash 4.0 或以上
  - 常用工具：`tar`, `grep`, `sed`, `awk`（視腳本需求而定）

## 使用方式
1. **克隆專案**：
   ```bash
   git clone https://github.com/your-username/shell-toolkit.git
   cd shell-toolkit
   ```

2. **設定腳本權限**：
   為腳本添加可執行權限，例如：
   ```bash
   chmod +x scripts/ksh/backup.ksh
   chmod +x scripts/bash/rename.sh
   ```

3. **執行腳本**：
   - KSH 腳本（AIX 環境）：
     ```bash
     ksh scripts/ksh/backup.ksh /path/to/source /path/to/backup
     ```
   - Bash 腳本（Linux/macOS）：
     ```bash
     ./scripts/bash/rename.sh /path/to/files
     ```

4. **檢查腳本說明**：
   每個腳本檔案開頭有註解，說明用途和用法。例如：
   ```bash
   # File: backup.ksh
   # Description: Backup specified directory to a tar.gz file
   # Usage: ksh backup.ksh <source_dir> <backup_dir>
   ```

## 腳本範例
- **KSH: backup.ksh**
  - 用途：將指定目錄備份為 tar.gz 檔案，適用於 AIX 環境。
  - 用法：`ksh scripts/ksh/backup.ksh /data /backup`
- **Bash: rename.sh**
  - 用途：批次重命名指定目錄中的檔案，支援正則表達式。
  - 用法：`./scripts/bash/rename.sh /path/to/files`

## 貢獻
歡迎提交新的腳本或改進現有腳本！請遵循以下步驟：
1. Fork 本專案。
2. 新增或修改腳本，確保包含清晰的註解和用法說明。
3. 提交 Pull Request，並描述你的變更。

## 問題回報
如有問題或建議，請在 GitHub 開 issue，或聯繫 [<EMAIL>]。

## 授權
本專案採用 MIT 授權，詳見 `LICENSE` 檔案。