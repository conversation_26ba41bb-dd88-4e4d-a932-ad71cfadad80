# shell-toolkit

一個存放常用 shell 腳本的工具集，主要包含適用於 AIX 系統的 KSH（KornShell）腳本，涵蓋資料庫管理、系統管理與自動化任務。

## 專案目標
- 提供實用的 shell 腳本，簡化日常工作流程
- 專注於 AIX 環境（KSH）的資料庫管理腳本
- 易於維護和擴展，適合資料庫管理員、系統管理員和 DevOps 人員

## 專案描述
本專案收集了在 AIX 環境下常用的 shell 腳本，特別針對資料庫管理任務。目前專注於 DB2 資料庫的表空間管理功能。

## 使用技術
- **Shell**: KornShell (ksh)
- **平台**: AIX 系統
- **資料庫**: IBM DB2

## 目錄結構
```
shell-toolkit/
├── scripts/
│   └── ksh/                      # AIX 系統適用的 KSH 腳本
│       └── unlock_tablespace.sh # 資料庫表空間解鎖腳本
├── README.md                     # 專案說明文件
└── LICENSE                       # Apache 2.0 授權文件
```

## 檔案清單
- **scripts/ksh/unlock_tablespace.sh**: 資料庫表空間解鎖腳本，用於批次執行多個資料庫的表空間備份命令

## 環境需求
- **作業系統**: AIX 系統（或支援 KSH 的環境）
- **Shell**: KornShell (ksh) 環境
- **資料庫**: IBM DB2 資料庫系統
- **權限**: 需要具備資料庫管理權限以執行表空間操作

## 安裝及執行方式

### 1. 克隆專案
```bash
git clone https://github.com/vancetang/shell-toolkit.git
cd shell-toolkit
```

### 2. 設定腳本權限
為腳本添加可執行權限：
```bash
chmod +x scripts/ksh/unlock_tablespace.sh
```

### 3. 執行腳本
在 AIX 環境中執行 KSH 腳本：
```bash
ksh scripts/ksh/unlock_tablespace.sh
```

或直接執行（如果已設定可執行權限）：
```bash
./scripts/ksh/unlock_tablespace.sh
```

### 4. 腳本說明
每個腳本都包含詳細的註解說明其用途和使用方法。

## 腳本詳細說明

### unlock_tablespace.sh
- **用途**: 批次執行多個資料庫的表空間線上備份命令，用於解鎖表空間
- **功能**:
  - 針對 US、JP、AU 三個資料庫執行操作
  - 對每個資料庫的 BAT_DATA_TS4k 和 ELB_DATA_TS4K 表空間執行線上備份
  - 備份輸出導向 /dev/null（僅執行解鎖操作，不保存備份檔案）
- **用法**: `ksh scripts/ksh/unlock_tablespace.sh`
- **輸出範例**:
  ```
  >>>> US <<<<
  -- BAT_DATA_TS4k
  backup db US tablespace BAT_DATA_TS4k online to /dev/null
  -- ELB_DATA_TS4K
  backup db US tablespace ELB_DATA_TS4K online to /dev/null
  >>>> JP <<<<
  -- BAT_DATA_TS4k
  backup db JP tablespace BAT_DATA_TS4k online to /dev/null
  -- ELB_DATA_TS4K
  backup db JP tablespace ELB_DATA_TS4K online to /dev/null
  >>>> AU <<<<
  -- BAT_DATA_TS4k
  backup db AU tablespace BAT_DATA_TS4k online to /dev/null
  -- ELB_DATA_TS4K
  backup db AU tablespace ELB_DATA_TS4K online to /dev/null
  ```

## 貢獻
歡迎提交新的腳本或改進現有腳本！請遵循以下步驟：
1. Fork 本專案
2. 建立功能分支 (`git checkout -b feature/new-script`)
3. 新增或修改腳本，確保包含清晰的註解和用法說明
4. 提交變更 (`git commit -am 'Add new script: description'`)
5. 推送到分支 (`git push origin feature/new-script`)
6. 建立 Pull Request，並詳細描述你的變更

## 問題回報
如有問題或建議，請在 GitHub 開 issue。

## 授權
本專案採用 Apache License 2.0 授權，詳見 `LICENSE` 檔案。

---

**版權所有 © 2024–Present Vance Tang**