
################################################################
#  Environmental Variables
################################################################

PROG_ID=${0##*/}
PROG_ID=${PROG_ID%.*}

NOW_TIME=`date +"%Y%m%d_%H%M%S"`
EXP_ROOT="/eloanap/batch/old_2023/maskdata_${NOW_TIME}"

LOG_HOME="${EXP_ROOT}"
LOGFILE="${LOG_HOME}"/${PROG_ID}.log

# function name
FNC_NM="ELN.MASKNM"

#### ALL SETTING ===========================================================
#徵信
TBS_ELI="ELI0CIG ELI0SIG ELIDMAS ELIDRCV ELIEFSM ELIEFSMM ELIFFSM ELIFFSMM ELIIAGR ELIIAOP ELIIBASC ELIIBASP ELIICDS ELIICOR ELIICSTD"

#授信
TBS_ELL="ELL0CIG ELL0LOG0 ELL0LOG1 ELL0LOG2 ELL0LOG3 ELL0LOG4 ELL0LOG5 ELL0LOG6 ELL0LOG7 ELL0LOG8 ELL0LOG9 ELL0SIG ELLACDGI ELLACDGN ELLACQT ELLACSL ELLADCI ELLADRB2 ELLADRB3 ELLALDID ELLALDIM ELLALRQ ELLAMHCD ELLAREL ELLASNT ELLBBEA ELLBMTB ELLBPRM ELLLACSL ELLLALC ELLLALCD2 ELLLALCP ELLLBAS ELLLBREL ELLLBYR ELLLCEG ELLLCRM ELLLEPBAS ELLLGME ELLLGOP ELLLGUA ELLLMAS ELLLMNR ELLLRDC ELLLRIDC ELLLRVK ELLLSLAM ELLLTEST ELLM401M ELLMCMB ELLMGUA ELLMGUAD ELLMMHRC ELLMMSH ELLMNCBL ELLZCUST"
## 授信刪除table: ELLLNRV, ELLZGUA,
## 授信增加table: ELLZCUST

#覆審
TBS_ELR="ELRACUST ELRAMSTR ELRALIST ELRACRD1 ELRACRD2 ELRATRK ELRBCUST ELRBLIST ELRBCRD1 ELRBTRK ELRCMRP ELRCRPT ELRENNLOAN ELREUNION ELREGROUPWARN"

# 每個table的欄位名稱
ELL0CIG="NAME"
ELL0LOG0="NAME"
ELL0LOG1="NAME"
ELL0LOG2="NAME"
ELL0LOG3="NAME"
ELL0LOG4="NAME"
ELL0LOG5="NAME"
ELL0LOG6="NAME"
ELL0LOG7="NAME"
ELL0LOG8="NAME"
ELL0LOG9="NAME"
ELL0SIG="NAME"
ELLACDGI="APPNAME"
ELLACDGN="CMNAME"
ELLACQT="APPNAME"
ELLACSL="RELCNM RELENM"
ELLADCI="APPNAME"
ELLADRB2="GPRNM"
ELLADRB3="RELNM"
ELLALDID="APPNAME"
ELLALDIM="GAONM GROUPNM"
ELLALRQ="CMNAME RNAME"
ELLAMHCD="APPNAME"
ELLAREL="RBCNAME RCNAME RTCNAME"
ELLASNT="APPNAME"
ELLBBEA="AONAME SRNAME"
ELLBMTB="EMPNM"
ELLBPRM="AONM MGNNM"
ELLLACSL="CPYCNM CPYENM"
ELLLALC="APPNAME APPRNAME"
ELLLALCD2="BFYNM"
ELLLALCP="CMBNM"
ELLLBAS="APPNAME CAREERNAME CMENAME CMNAME CMRNAME ENAME GROUPNM GRPNM"
ELLLBREL="RELNM"
ELLLBYR="APPNAME CMNAME ENAME GROUPNM"
ELLLCEG="GTNM"
ELLLCRM="MTHRNM"
ELLLEPBAS="APPNAME"
ELLLGME="GMENM"
ELLLGOP="GMENM"
ELLLGUA="APPNAME ENAME RNAME"
ELLLMAS="APPNAME APPRNAME"
ELLLMNR="BANNM"
#ELLLNRV="APPNAME APPRNM BOSSNM CLSNM CNTNM"
ELLLRDC="APPNAME"
ELLLRIDC="APPNAME"
ELLLRVK="APPNAME APPRNAME BRCNAME"
ELLLSLAM="APPNAME CMNAME"
ELLLTEST="CNAME"
ELLM401M="APPNAME"
ELLMCMB="CMBNM"
ELLMGUA="APPNAME"
ELLMGUAD="APPNAME ENAME RNAME"
ELLMMHRC="CUSTNAME"
ELLMMSH="APPNAME CMNAME"
ELLMNCBL="APPNAME"
#ELLZGUA="APPNAME"
ELLZCUST="APPNAME"
ELI0CIG="NAME"
ELI0SIG="NAME"
ELIDMAS="APPNAME APPRNAME"
ELIDRCV="CUSTNAME AO_NAME APPROVE_NAME"
ELIEFSM="APPNAME APPRNAME"
ELIEFSMM="APPNAME APPRNAME"
ELIFFSM="APPNAME APPRNAME"
ELIFFSMM="APPNAME APPRNAME"
ELIIAGR="FACNM"
ELIIAOP="NAME"
ELIIBASC="APPNAME ENAME GROUPNM CRGCNAME CRGRNAME CRGENAME"
ELIIBASP="APPNAME RNAME ENAME CAREERNAME"
ELIICDS="NAME LNAME"
ELIICOR="CORNM MSNM"
ELIICSTD="CUSNAME"

ELRACUST="APPNAME"
ELRAMSTR="APPRNAME"
ELRALIST="APPNAME CHIEFNM APPRNAME BAPPNAME"
ELRACRD1="APPNAME CMNAME APPRNAME"
ELRACRD2="APPNAME CMNAME APPRNAME"
ELRATRK="APPNAME APPRNAME"
ELRBCUST="APPNAME"
ELRBLIST="APPNAME"
ELRBCRD1="APPNAME"
ELRBTRK="APPNAME"
ELRCMRP="APPNAME"
ELRCRPT="APPNAME PAPPNAME"
ELRENNLOAN="APPNAME"
ELREUNION="APPNAME"
ELREGROUPWARN="APPNAME GRPNM GAONM BANNM CMPNM"

################################################################
#  function
################################################################

log(){
	local LOG_TS=`date +"%Y/%m/%d %H:%M:%S"`
	echo "[${LOG_TS}][$(whoami)] ${1}" | tee -a ${LOGFILE}
}

entrylog(){
	local currentts=`date +"%Y%m%d_%H%M%S"`
    log ""
    log "[$currentts]===============($1) BEGIN ==============="
}

logWarn(){
    log "!!!!!!!!!![WARN-BEGIN]!!!!!!!!!!"
    log "[WARN] $1"
    log "!!!!!!!!!![ WARN-END ]!!!!!!!!!!"
}

logError(){
    log "!!!!!!!!!!!!!!!!!!!!<<ERROR-BEGIN>>!!!!!!!!!!!!!!!!!!!!"
    log "[ERROR] $1"
    log "!!!!!!!!!!!!!!!!!!!!<< ERROR-END >>!!!!!!!!!!!!!!!!!!!!"
}

getUUID() {
    local RT_UUID=`uuidgen | tr 'a-z' 'A-Z' | sed 's/-//g'`
    echo ${RT_UUID}
}

runDB2Cmd(){
    log "---------------------------------------------"
    log "SQL=${1}"
    
    local T_UID_F="${LOG_HOME}/$(getUUID)"
    
    db2 "${1}" > ${T_UID_F} 
    local runDB2CmdRC=$?
    cat ${T_UID_F} | tee -a ${LOGFILE}
    
    # 0 or 1 為正確忽略
    if [[ ${runDB2CmdRC} == 2 ]]; then
        logWarn "有警告訊息請注意(rc=${runDB2CmdRC})"
        log ""
    elif [[ ${runDB2CmdRC} -gt 2 ]]; then
        logError "有錯誤訊息請注意(rc=${runDB2CmdRC})"
        log ""
    fi
    rm ${T_UID_F}
    return ${runDB2CmdRC}
}

################################################################
#  main
################################################################

rm -r ${EXP_ROOT} 2>/dev/null
mkdir ${EXP_ROOT}

TBS=""
ACTION_TYPE=$(echo $1 | tr 'A-Z' 'a-z')
if [[ ${ACTION_TYPE} == "" ]]; then
	ACTION_TYPE="all"
fi
case $ACTION_TYPE in
  "eli")
    TBS="${TBS_ELI}"
  ;;
  "ell")
    TBS="${TBS_ELL}"
  ;;
    "elr")
    TBS="${TBS_ELR}"
  ;;
  "all")
    TBS="${TBS_ELI} ${TBS_ELL} ${TBS_ELR}"
  ;;
  *)
    SH_NM=$0
    PARM=$@
    log "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    log "[ERROR] [無對應類別] sh ${SH_NM} ${PARM}"
    log "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
	exit
  ;;
esac

entrylog "本次執行模式(${ACTION_TYPE})"
log "table-list=[${TBS}]"

runDB2Cmd "connect to tw"

#建立masknm function(這裡有個要注意的點，SQL的pattern"$"是 shell的特殊字元，所以要特別加上一個\跳脫字元)
SQL="
CREATE OR REPLACE FUNCTION ${FNC_NM} (NM VARCHAR(2048)) RETURNS VARCHAR(2048)
    BEGIN
        DECLARE MASKNM VARCHAR(2048);
        -- 這裡先把NULL轉空白，在TRIM一下
        SET MASKNM=TRIM(COALESCE(NM, ''));
        IF LENGTH(MASKNM) = 0 THEN
            RETURN MASKNM;
        END IF;
        -- v11.5有支援，太老舊沒有支援注意
        RETURN REGEXP_REPLACE(MASKNM, '(^.{1})(.{1})', '\$1\*');
    END
"
runDB2Cmd "${SQL}"
runDB2Cmd "COMMENT ON FUNCTION ${FNC_NM} IS '資料隱碼(姓名第二碼為*)'"

for TBNAME in ${TBS}
do
	entrylog "${TBNAME}開始"
		
	#先將每個要替換的欄位名稱空白替換為', '，轉為 XX', 'AA', 'BBB
	RP_COLS=$(echo "$(eval echo "\$$TBNAME")" | sed "s/ /', '/g")
	#組SQL字串(這裡之後可以改成多種不同的組合)
	TMP_SQL="case when COLNAME in ('${RP_COLS}') then 'ELN.MASKNM('|| COLNAME ||')' else COLNAME end"
	
	#從syscat.columns中抓取table欄位名稱
	TBCOLS=$(db2 -x "select LISTAGG(${TMP_SQL}, ', ') WITHIN GROUP (ORDER BY COLNO ASC) from syscat.COLUMNS where TABSCHEMA='ELN' and TABNAME='${TBNAME}'")
	#去除頭尾空白
	TBCOLS=$(echo "$TBCOLS" | xargs)
	
	if [[ ${TBCOLS} == "-" ]]; then
		logError "${TBNAME}異常-(請檢視是否建立該table)"	
	else
		runDB2Cmd "export to ${EXP_ROOT}/${TBNAME}.ixf of ixf lobfile ${TBNAME} modified by lobsinfile select ${TBCOLS} from eln.${TBNAME}"
		#runDB2Cmd "export to ${EXP_ROOT}/${TBNAME}.ixf of ixf select ${TBCOLS} from eln.${TBNAME}"
		rc=$?
		if [[ ${rc} == 0 ]]; then
			runDB2Cmd "load from ${EXP_ROOT}/${TBNAME}.ixf of ixf lobs from ${EXP_ROOT} REPLACE INTO ELN.${TBNAME} nonrecoverable"
			#runDB2Cmd "load client from ${EXP_ROOT}/${TBNAME}.ixf of ixf REPLACE INTO ELN.${TBNAME} nonrecoverable"
			runDB2Cmd "runstats on table eln.${TBNAME} on all columns with distribution and detailed indexes all"
		fi
	fi	
done

# 不刪除已建立function
# runDB2Cmd "drop function ${FNC_NM}"

runDB2Cmd "terminate"

